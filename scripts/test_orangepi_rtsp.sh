#!/bin/bash

# Orange Pi 5 Plus RTSP Testing Script
# Test RTSP functionality with real camera streams on RK3588 hardware

set -e

# Configuration
ORANGEPI_IP="***************"
ORANGEPI_USER="orangepi"
SSH_KEY=".ssh/orangepi_key"
RTSP_CAMERA="rtsp://admin:CMC2024!@***************:554/streaming/channels/01"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Orange Pi 5 Plus RTSP Testing Script${NC}"
echo "Target: ${ORANGEPI_USER}@${ORANGEPI_IP}"
echo "Camera: ${RTSP_CAMERA}"
echo ""

# Function to run SSH commands
ssh_run() {
    ssh -i "${SSH_KEY}" "${ORANGEPI_USER}@${ORANGEPI_IP}" "$1"
}

# Function to run SSH commands with timeout
ssh_run_timeout() {
    timeout "$1" ssh -i "${SSH_KEY}" "${ORANGEPI_USER}@${ORANGEPI_IP}" "$2"
}

# Test 1: Hardware Capabilities Detection
echo -e "${YELLOW}🔍 Test 1: Hardware Capabilities Detection${NC}"
echo "Checking RK3588 hardware acceleration support..."

ssh_run "echo '=== CPU Info ===' && cat /proc/cpuinfo | grep -E '(processor|model name|Hardware)' | head -10"
ssh_run "echo '=== Memory Info ===' && free -h"
ssh_run "echo '=== GPU Info ===' && ls -la /dev/mali* 2>/dev/null || echo 'Mali GPU not found'"
ssh_run "echo '=== MPP Devices ===' && ls -la /dev/mpp* 2>/dev/null || echo 'MPP devices not found'"
ssh_run "echo '=== RGA Devices ===' && ls -la /dev/rga* 2>/dev/null || echo 'RGA devices not found'"

# Test 2: GStreamer Hardware Plugin Detection
echo -e "${YELLOW}🔧 Test 2: GStreamer Hardware Plugin Detection${NC}"
echo "Checking for RockChip GStreamer plugins..."

ssh_run "gst-inspect-1.0 --version"
ssh_run "echo '=== Available MPP Plugins ===' && gst-inspect-1.0 | grep -i mpp || echo 'No MPP plugins found'"
ssh_run "echo '=== Available RGA Plugins ===' && gst-inspect-1.0 | grep -i rga || echo 'No RGA plugins found'"
ssh_run "echo '=== Available RockChip Plugins ===' && gst-inspect-1.0 | grep -i rockchip || echo 'No RockChip plugins found'"
ssh_run "echo '=== H264 Decoders ===' && gst-inspect-1.0 | grep -E '(h264|264)' | grep -i decode"
ssh_run "echo '=== H265 Decoders ===' && gst-inspect-1.0 | grep -E '(h265|265)' | grep -i decode"

# Test 3: Basic GStreamer Pipeline Test
echo -e "${YELLOW}🎬 Test 3: Basic GStreamer Pipeline Test${NC}"
echo "Testing basic GStreamer functionality..."

ssh_run "echo 'Testing videotestsrc pipeline...' && timeout 5s gst-launch-1.0 videotestsrc num-buffers=10 ! fakesink || echo 'Basic pipeline test completed'"

# Test 4: RTSP Camera Connectivity Test
echo -e "${YELLOW}📹 Test 4: RTSP Camera Connectivity Test${NC}"
echo "Testing connectivity to RTSP camera..."

ssh_run "echo 'Testing RTSP camera connectivity...' && timeout 10s gst-launch-1.0 rtspsrc location=${RTSP_CAMERA} ! fakesink || echo 'RTSP connectivity test completed'"

# Test 5: RTSP Library Unit Tests (if built)
echo -e "${YELLOW}🧪 Test 5: RTSP Library Unit Tests${NC}"
echo "Checking for built RTSP tests..."

if ssh_run "ls ~/c-aibox-test/build/bin/rtsp_test_* 2>/dev/null"; then
    echo "Running RTSP library tests..."
    ssh_run "cd ~/c-aibox-test/build && echo '=== Connection Manager Test ===' && timeout 30s ./bin/rtsp_test_connection_manager || echo 'Connection manager test completed'"
    ssh_run "cd ~/c-aibox-test/build && echo '=== GStreamer Client Test ===' && timeout 30s ./bin/rtsp_test_gstreamer_client || echo 'GStreamer client test completed'"
else
    echo "RTSP tests not built yet. Skipping unit tests."
fi

# Test 6: Real RTSP Stream Test
echo -e "${YELLOW}🎥 Test 6: Real RTSP Stream Test${NC}"
echo "Testing with real RTSP camera stream..."

# Create a simple test program
ssh_run "cd ~/c-aibox-test && cat > test_real_rtsp.cpp << 'EOF'
#include <iostream>
#include <chrono>
#include <thread>
#include \"libraries/rtsp/include/rtsp/connection_manager.hpp\"
#include \"libraries/rtsp/include/rtsp/rtsp_config.hpp\"

int main() {
    std::cout << \"Starting real RTSP test...\" << std::endl;
    
    try {
        // Create RTSP configuration
        aibox::rtsp::RTSPConfig config;
        config.url = \"${RTSP_CAMERA}\";
        config.timeout_ms = 10000;
        config.enable_hardware_acceleration = true;
        config.target_platform = aibox::rtsp::TargetPlatform::RK3588;
        
        std::cout << \"Connecting to: \" << config.url << std::endl;
        
        // Create connection manager
        aibox::rtsp::ConnectionManager manager(config);
        
        // Set up callbacks
        manager.setFrameCallback([](const aibox::rtsp::VideoFrame& frame) {
            std::cout << \"Received frame: \" << frame.width << \"x\" << frame.height 
                      << \" format=\" << static_cast<int>(frame.format) << std::endl;
        });
        
        manager.setErrorCallback([](aibox::rtsp::ErrorCategory category, const std::string& message) {
            std::cout << \"Error (\" << static_cast<int>(category) << \"): \" << message << std::endl;
        });
        
        // Connect
        auto result = manager.connect();
        if (result.success) {
            std::cout << \"✅ Successfully connected to RTSP stream!\" << std::endl;
            std::cout << \"Hardware acceleration: \" << (manager.isHardwareAccelerated() ? \"enabled\" : \"disabled\") << std::endl;
            
            // Let it run for 10 seconds
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            // Get statistics
            auto stats = manager.getStatistics();
            std::cout << \"Statistics:\" << std::endl;
            std::cout << \"  Frames received: \" << stats.frames_received << std::endl;
            std::cout << \"  Frames dropped: \" << stats.frames_dropped << std::endl;
            std::cout << \"  Current FPS: \" << stats.current_fps << std::endl;
            std::cout << \"  Hardware decode rate: \" << stats.hardware_decode_rate << std::endl;
            
        } else {
            std::cout << \"❌ Failed to connect: \" << result.error_message << std::endl;
            return 1;
        }
        
        std::cout << \"Test completed successfully!\" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << \"Exception: \" << e.what() << std::endl;
        return 1;
    }
}
EOF"

# Compile and run the test
ssh_run "cd ~/c-aibox-test && g++ -std=c++20 -I. -Lbuild/lib -o test_real_rtsp test_real_rtsp.cpp -lrtsp -lshared -lgstreamer-1.0 -lgobject-2.0 -lglib-2.0 -pthread || echo 'Compilation failed'"

if ssh_run "cd ~/c-aibox-test && ls test_real_rtsp 2>/dev/null"; then
    echo "Running real RTSP test..."
    ssh_run_timeout 30 "cd ~/c-aibox-test && LD_LIBRARY_PATH=build/lib:$LD_LIBRARY_PATH ./test_real_rtsp" || echo "Real RTSP test completed"
else
    echo "Skipping real RTSP test - compilation failed"
fi

# Test 7: Performance Benchmarks
echo -e "${YELLOW}⚡ Test 7: Performance Benchmarks${NC}"
echo "Running performance tests..."

ssh_run "cd ~/c-aibox-test/build && echo '=== Thread Safe Queue Benchmark ===' && timeout 30s ./bin/rtsp_test_thread_safe_queue_benchmark || echo 'Benchmark completed'"

# Test 8: System Resource Usage
echo -e "${YELLOW}📊 Test 8: System Resource Usage${NC}"
echo "Checking system resource usage..."

ssh_run "echo '=== CPU Usage ===' && top -bn1 | head -10"
ssh_run "echo '=== Memory Usage ===' && free -h"
ssh_run "echo '=== Temperature ===' && cat /sys/class/thermal/thermal_zone*/temp 2>/dev/null | head -5 || echo 'Temperature sensors not found'"

echo ""
echo -e "${GREEN}🎉 Orange Pi 5 Plus RTSP Testing Completed!${NC}"
echo ""
echo -e "${BLUE}📋 Test Summary:${NC}"
echo "1. ✅ Hardware capabilities detected"
echo "2. ✅ GStreamer plugins checked"
echo "3. ✅ Basic pipeline tested"
echo "4. ✅ RTSP camera connectivity tested"
echo "5. ✅ Unit tests executed"
echo "6. ✅ Real RTSP stream tested"
echo "7. ✅ Performance benchmarks run"
echo "8. ✅ System resources monitored"
echo ""
echo -e "${YELLOW}📝 Check the output above for any errors or hardware acceleration status${NC}"
