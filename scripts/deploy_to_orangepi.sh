#!/bin/bash

# Orange Pi 5 Plus Deployment Script
# Deploy and test RTSP module on RK3588 hardware

set -e

# Configuration
ORANGEPI_IP="***************"
ORANGEPI_USER="orangepi"
SSH_KEY=".ssh/orangepi_key"
RTSP_CAMERA="rtsp://admin:CMC2024!@***************:554/streaming/channels/01"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Orange Pi 5 Plus RTSP Deployment Script${NC}"
echo "Target: ${ORANGEPI_USER}@${ORANGEPI_IP}"
echo "Camera: ${RTSP_CAMERA}"
echo ""

# Function to run SSH commands
ssh_run() {
    ssh -i "${SSH_KEY}" "${ORANGEPI_USER}@${ORANGEPI_IP}" "$1"
}

# Function to copy files
scp_copy() {
    scp -i "${SSH_KEY}" -r "$1" "${ORANGEPI_USER}@${ORANGEPI_IP}:$2"
}

# Step 1: Check Orange Pi connectivity
echo -e "${YELLOW}📡 Step 1: Checking Orange Pi connectivity...${NC}"
if ssh_run "echo 'Orange Pi connected successfully'"; then
    echo -e "${GREEN}✅ Orange Pi connection successful${NC}"
else
    echo -e "${RED}❌ Failed to connect to Orange Pi${NC}"
    exit 1
fi

# Step 2: Check system info
echo -e "${YELLOW}🔍 Step 2: Checking Orange Pi system info...${NC}"
ssh_run "uname -a"
ssh_run "cat /proc/cpuinfo | grep 'model name' | head -1"
ssh_run "free -h"
ssh_run "df -h /"

# Step 3: Check existing dependencies on Orange Pi
echo -e "${YELLOW}📦 Step 3: Checking existing dependencies on Orange Pi...${NC}"
echo "Checking for build tools..."
ssh_run "which cmake || echo 'cmake not found'"
ssh_run "which pkg-config || echo 'pkg-config not found'"
ssh_run "which g++ || echo 'g++ not found'"
ssh_run "which gst-launch-1.0 || echo 'gstreamer not found'"
echo ""
echo -e "${YELLOW}⚠️  Note: If dependencies are missing, please install them manually with:${NC}"
echo "sudo apt update"
echo "sudo apt install -y build-essential cmake pkg-config git"
echo "sudo apt install -y libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev"
echo "sudo apt install -y gstreamer1.0-plugins-base gstreamer1.0-plugins-good"
echo ""

# Step 4: Create project directory
echo -e "${YELLOW}📁 Step 4: Creating project directory...${NC}"
ssh_run "mkdir -p ~/c-aibox-test"
ssh_run "rm -rf ~/c-aibox-test/*"

# Step 5: Copy source code
echo -e "${YELLOW}📤 Step 5: Copying source code to Orange Pi...${NC}"
echo "Copying libraries..."
scp_copy "libraries/" "~/c-aibox-test/"
echo "Copying cmake..."
scp_copy "cmake/" "~/c-aibox-test/"
echo "Copying CMakeLists.txt..."
scp_copy "CMakeLists.txt" "~/c-aibox-test/"
echo "Copying examples..."
scp_copy "examples/" "~/c-aibox-test/"

# Step 6: Build on Orange Pi (if tools available)
echo -e "${YELLOW}🔨 Step 6: Building project on Orange Pi...${NC}"
if ssh_run "which cmake && which g++"; then
    echo "Build tools found, proceeding with build..."
    ssh_run "cd ~/c-aibox-test && mkdir -p build && cd build"
    ssh_run "cd ~/c-aibox-test/build && cmake .. -DCMAKE_BUILD_TYPE=Release" || echo "CMake configuration failed"
    ssh_run "cd ~/c-aibox-test/build && make -j\$(nproc) rtsp" || echo "Build failed"
else
    echo -e "${YELLOW}⚠️  Build tools not available. Skipping build step.${NC}"
    echo "Please install dependencies first and run the build manually."
fi

# Step 7: Test GStreamer detection (if available)
echo -e "${YELLOW}🧪 Step 7: Testing GStreamer detection...${NC}"
if ssh_run "which gst-inspect-1.0"; then
    ssh_run "gst-inspect-1.0 --version"
    ssh_run "gst-inspect-1.0 | grep -E '(mpp|rga|rockchip)' || echo 'No RockChip plugins found'"
else
    echo -e "${YELLOW}⚠️  GStreamer not available. Skipping GStreamer tests.${NC}"
fi

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}🎯 Next: Run test_orangepi_rtsp.sh to test RTSP functionality${NC}"
