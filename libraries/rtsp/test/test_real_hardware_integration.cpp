#include <gtest/gtest.h>
#include "rtsp/rtsp_config.hpp"
#include "rtsp/connection_manager.hpp"

using namespace aibox::rtsp;

class RealHardwareIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Real Orange Pi 5 Plus configuration from devices.md
        setupOrangePiConfig();
        setupRealCameraConfig();
    }

    void setupOrangePiConfig() {
        // Orange Pi 5 Plus (RK3588) - 192.168.222.100
        orange_pi_config_.enabled = true;
        orange_pi_config_.platform_override = "4gb";

        // Performance configuration for 4GB variant
        orange_pi_config_.performance.configureFor4GB();
        orange_pi_config_.performance.thread_pool_size = 4;
        orange_pi_config_.performance.cpu_affinity = {2, 3};  // A55 cores

        // Hardware acceleration settings
        orange_pi_config_.performance.enable_mpp_decoder = true;
        orange_pi_config_.performance.enable_rga_scaler = true;
        orange_pi_config_.performance.enable_dmabuf_zerocopy = true;

        // Thermal management for RK3588
        orange_pi_config_.performance.thermal_management = true;
        orange_pi_config_.performance.thermal_throttle_temperature = 80;
        orange_pi_config_.performance.thermal_shutdown_temperature = 85;

        // Monitoring settings
        orange_pi_config_.monitoring.enable_statistics = true;
        orange_pi_config_.monitoring.statistics_interval_ms = 1000;
        orange_pi_config_.monitoring.enable_performance_monitoring = true;
        orange_pi_config_.monitoring.enable_thermal_monitoring = true;
        orange_pi_config_.monitoring.enable_memory_monitoring = true;
    }

    void setupRealCameraConfig() {
        // Real camera from devices.md: 192.168.222.169:554/streaming/channels/01
        real_camera_config_.rtsp_url = "rtsp://192.168.222.169:554/streaming/channels/01";
        real_camera_config_.username = "admin";
        real_camera_config_.password = "CMC2024!";
        real_camera_config_.transport = TransportProtocol::TCP;
        real_camera_config_.timeout_ms = 5000;
        real_camera_config_.retry_count = 3;
        real_camera_config_.retry_delay_ms = 1000;
        real_camera_config_.buffer_size_bytes = 1024 * 1024;  // 1MB buffer
        real_camera_config_.enabled = true;
    }

    RTSPModuleConfig orange_pi_config_;
    RTSPConnectionConfig real_camera_config_;
};

// Test Orange Pi configuration validation
TEST_F(RealHardwareIntegrationTest, OrangePiConfigurationValidation) {
    EXPECT_TRUE(orange_pi_config_.isValid());
    EXPECT_TRUE(orange_pi_config_.enabled);
    EXPECT_EQ(orange_pi_config_.platform_override, "4gb");

    // Validate performance configuration
    EXPECT_EQ(orange_pi_config_.performance.max_concurrent_streams, 6);
    EXPECT_EQ(orange_pi_config_.performance.max_memory_usage_mb, 1200);
    EXPECT_TRUE(orange_pi_config_.performance.enable_mpp_decoder);
    EXPECT_TRUE(orange_pi_config_.performance.enable_rga_scaler);
    EXPECT_TRUE(orange_pi_config_.performance.enable_dmabuf_zerocopy);

    // Validate CPU core assignment
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity.size(), 2);
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity[0], 2);
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity[1], 3);

    // Validate thermal management
    EXPECT_TRUE(orange_pi_config_.performance.thermal_management);
    EXPECT_EQ(orange_pi_config_.performance.thermal_throttle_temperature, 80);
    EXPECT_EQ(orange_pi_config_.performance.thermal_shutdown_temperature, 85);
}

// Test real camera configuration
TEST_F(RealHardwareIntegrationTest, RealCameraConfigurationValidation) {
    EXPECT_TRUE(real_camera_config_.isValid());
    EXPECT_EQ(real_camera_config_.rtsp_url, "rtsp://192.168.222.169:554/streaming/channels/01");
    EXPECT_EQ(real_camera_config_.username, "admin");
    EXPECT_EQ(real_camera_config_.password, "CMC2024!");
    EXPECT_EQ(real_camera_config_.transport, TransportProtocol::TCP);
    EXPECT_TRUE(real_camera_config_.enabled);
}

// Test connection manager with real camera configuration
TEST_F(RealHardwareIntegrationTest, ConnectionManagerWithRealCamera) {
    ConnectionManager manager(real_camera_config_);

    // Test initial state
    EXPECT_EQ(manager.getState(), ConnectionState::DISCONNECTED);
    EXPECT_FALSE(manager.isConnected());

    // Test basic functionality
    auto stats = manager.getStatistics();
    EXPECT_EQ(stats.packets_received, 0);
    EXPECT_EQ(stats.bytes_received, 0);
}

// Test memory usage estimation
TEST_F(RealHardwareIntegrationTest, MemoryUsageEstimation) {
    // Test single stream memory estimation
    size_t single_stream_memory = real_camera_config_.getEstimatedMemoryUsage();
    EXPECT_GT(single_stream_memory, 0);
    EXPECT_LT(single_stream_memory, 100 * 1024 * 1024);  // Should be less than 100MB per stream

    // Test total memory for Orange Pi 4GB configuration
    size_t total_memory = orange_pi_config_.getTotalEstimatedMemoryUsage();
    EXPECT_LE(total_memory, orange_pi_config_.performance.max_memory_usage_mb * 1024 * 1024);
}

// Test configuration manager functionality
TEST_F(RealHardwareIntegrationTest, ConfigurationManagerTest) {
    // Test creating default configuration
    auto default_config = RTSPConfigManager::createDefault();
    EXPECT_TRUE(default_config.isValid());

    // Test creating 4GB configuration
    auto config_4gb = RTSPConfigManager::createFor4GB();
    EXPECT_TRUE(config_4gb.isValid());
    EXPECT_EQ(config_4gb.performance.max_concurrent_streams, 6);
    EXPECT_EQ(config_4gb.performance.max_memory_usage_mb, 1200);
}

// Test real device network connectivity (basic test)
TEST_F(RealHardwareIntegrationTest, NetworkConnectivityTest) {
    // Test that we can create a connection manager with real camera config
    ConnectionManager manager(real_camera_config_);

    // Verify the configuration was set correctly
    EXPECT_EQ(manager.getState(), ConnectionState::DISCONNECTED);
    EXPECT_FALSE(manager.isConnected());

    // Test that the URL is properly formatted for the real camera
    EXPECT_TRUE(real_camera_config_.rtsp_url.find("192.168.222.169") != std::string::npos);
    EXPECT_TRUE(real_camera_config_.rtsp_url.find("554") != std::string::npos);
    EXPECT_TRUE(real_camera_config_.rtsp_url.find("streaming/channels/01") != std::string::npos);
}

// Test platform-specific configuration
TEST_F(RealHardwareIntegrationTest, PlatformSpecificConfiguration) {
    // Test that Orange Pi configuration is properly set up
    EXPECT_EQ(orange_pi_config_.platform_override, "4gb");
    EXPECT_TRUE(orange_pi_config_.performance.enable_mpp_decoder);
    EXPECT_TRUE(orange_pi_config_.performance.enable_rga_scaler);
    EXPECT_TRUE(orange_pi_config_.performance.enable_dmabuf_zerocopy);

    // Test CPU affinity for RK3588
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity.size(), 2);
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity[0], 2);  // A55 core
    EXPECT_EQ(orange_pi_config_.performance.cpu_affinity[1], 3);  // A55 core

    // Test thermal management settings
    EXPECT_TRUE(orange_pi_config_.performance.thermal_management);
    EXPECT_EQ(orange_pi_config_.performance.thermal_throttle_temperature, 80);
    EXPECT_EQ(orange_pi_config_.performance.thermal_shutdown_temperature, 85);
}
